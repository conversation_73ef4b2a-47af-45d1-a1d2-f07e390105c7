"use server";

import type { ExtendedUserDetails } from "@/hooks/use-user";
import { createAdminClient } from "@/lib/server/appwrite";
import {
  AGENTS_COLLECTION_ID,
  CHEFS_COLLECTION_ID,
  CITIZENS_COLLECTION_ID,
  DAT<PERSON>ASE_ID,
  QUARTIERS_COLLECTION_ID,
} from "@/lib/server/database";
import { Query } from "node-appwrite";
import { STATUS } from "./constants";

export async function getUserDetails(
  userId: string | undefined
): Promise<ExtendedUserDetails | null> {
  if (!userId) return null;

  try {
    const { databases, users } = await createAdminClient();

    // Récupérer l'utilisateur Appwrite
    const user = await users.get(userId);

    // Collections à vérifier selon le rôle
    const collections = [
      CITIZENS_COLLECTION_ID,
      AGENTS_COLLECTION_ID,
      CHEFS_COLLECTION_ID,
    ];

    // Rechercher les détails dans les collections
    for (const collectionId of collections) {
      const { documents } = await databases.listDocuments(
        DATABASE_ID,
        collectionId,
        [Query.equal("userId", userId)]
      );

      if (documents.length > 0) {
        const details = documents[0];

        // Récupérer les informations du quartier si disponible
        let quartierInfo = null;
        if (details.quartier) {
          try {
            // Pour les citoyens, utiliser quartierId si disponible, sinon chercher par nom
            if (details.quartierId) {
              quartierInfo = await databases.getDocument(
                DATABASE_ID,
                QUARTIERS_COLLECTION_ID,
                details.quartierId
              );
            } else {
              // Fallback: chercher par nom de quartier
              const quartierQuery = await databases.listDocuments(
                DATABASE_ID,
                QUARTIERS_COLLECTION_ID,
                [Query.equal("nom", details.quartier), Query.limit(1)]
              );
              if (quartierQuery.documents.length > 0) {
                quartierInfo = quartierQuery.documents[0];
              }
            }
          } catch (error) {
            console.warn("Erreur lors de la récupération du quartier:", error);
          }
        }

        return {
          ...user,
          quartier: quartierInfo
            ? {
                id: quartierInfo.$id,
                name: quartierInfo.nom,
              }
            : details.quartier
            ? {
                id: details.quartierId || "",
                name: details.quartier,
              }
            : undefined,
          role: details.role,
          userStatus: details.status as STATUS,
          phoneNumber: details.telephone,
        };
      }
    }

    // Si aucun détail trouvé, retourner l'utilisateur de base
    return user as ExtendedUserDetails;
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des détails utilisateur:",
      error
    );
    throw error;
  }
}
