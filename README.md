# NCR - Numérisation Complète de l'État Civil 🏛️

Application moderne de gestion numérique des services d'état civil en République de Guinée. Une solution complète et innovante pour la transformation digitale de l'administration publique.

## 🌟 Services Proposés

- **Certificats de Résidence** 📋

  - Demande et suivi en ligne
  - Validation électronique
  - Vérification QR code

- **Actes de Naissance** 👶

  - Déclaration de naissance
  - Copies d'actes numériques
  - Rectifications et mises à jour

- **Actes de Mariage** 💑

  - Publications des bans
  - Célébration numérique
  - Copies d'actes certifiées

- **Certificats de Nationalité** 🏳️

  - Demande en ligne
  - Suivi du dossier
  - Authentification sécurisée

- **Actes de Décès** 📜
  - Déclaration en ligne
  - Copies numériques
  - Mise à jour des registres

## 🚀 Technologies Utilisées

- **Framework**: [Next.js 15](https://nextjs.org/)
- **Language**: [TypeScript](https://www.typescriptlang.org/)
- **Gestionnaire de Paquets**: [pnpm](https://pnpm.io/)
- **Backend**: [Appwrite](https://appwrite.io/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **State Management**: [TanStack Query](https://tanstack.com/query)
- **Animations**: [Framer Motion](https://www.framer.com/motion/)
- **Formulaires**: [React Hook Form](https://react-hook-form.com/)
- **Validation**: [Zod](https://zod.dev/)
- **Icons**: [Lucide React](https://lucide.dev/)

## 📦 Installation

```bash
# Cloner le projet
git clone https://github.com/votre-organisation/ncr-new.git
cd ncr-new

# Installer les dépendances avec pnpm
pnpm install
```

## 🔧 Configuration

### Variables d'Environnement

1. Créez un fichier `.env.local` à la racine du projet :

```bash
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_APPWRITE_ENDPOINT=http://localhost/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
```

### Environnements Disponibles

L'application supporte deux environnements principaux :

- **Development** (`development`)
  - URL: `http://localhost:3000`
  - API: `http://localhost:3000/api`
- **Production** (`production`)
  - URL: `https://ncr.ouestech.com`
  - API: `https://ncr.ouestech.com/api`

## 🏃‍♂️ Démarrage

```bash
# Développement
pnpm dev

# Construction
pnpm build

# Production
pnpm start
```

## 📁 Structure du Projet

```
ncr-new/
├── src/
│   ├── app/                 # Pages et routes Next.js
│   ├── components/          # Composants React réutilisables
│   ├── config/             # Configuration (env, seo)
│   ├── lib/                # Utilitaires et services
│   ├── providers/          # Providers React
│   └── types/              # Types TypeScript
├── public/                 # Assets statiques
└── tailwind.config.ts     # Configuration Tailwind CSS
```

## 🔐 Sécurité

- Validation des variables d'environnement
- Gestion sécurisée des URLs selon l'environnement
- Intégration sécurisée avec Appwrite
- Protection des routes sensibles

## 🌐 SEO

L'application intègre une configuration SEO complète :

- Métadonnées dynamiques par page
- Support OpenGraph et Twitter Cards
- Données structurées (JSON-LD)
- Manifest PWA
- Balises canoniques

## 📱 Progressive Web App (PWA)

L'application est configurée comme une PWA avec :

- Manifest complet
- Icons pour différentes tailles
- Thème et couleurs personnalisés
- Support hors ligne (à venir)

## 🧪 Tests

```bash
# Lancer les tests
pnpm test

# Tests avec couverture
pnpm test:coverage
```

## 📚 Documentation API

La documentation de l'API est disponible aux endpoints suivants :

- Development: `http://localhost:3000/api/docs`
- Production: `https://ncr.ouestech.com/api/docs`

## 🤝 Contribution

1. Fork le projet
2. Créez votre branche (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## 📄 License

Ce projet est sous licence [MIT](LICENSE).

## 👥 Équipe

- **République de Guinée** - _Organisation Initiale_

## 📞 Support

Pour toute question ou problème :

- 📧 Email: <EMAIL>
- 🌐 Site: https://ncr.ouestech.com
- 📱 Téléphone: +224 XX XXX XX XX

## 🔄 État du Projet

![Statut Build](https://img.shields.io/badge/build-passing-brightgreen)
![Version](https://img.shields.io/badge/version-0.1.0-blue)
![License](https://img.shields.io/badge/license-MIT-green)

## 🏗️ Architecture du Système d'Inscription

### 📊 Modèle de Données

Le système utilise une architecture distribuée avec trois collections principales dans Appwrite :

#### 1. Collection `citizens`

- Stockage des informations personnelles
- Champs principaux :
  - `userId`: Lien vers le compte utilisateur
  - Informations d'identité (nom, prénom, date de naissance, etc.)
  - Contacts (téléphone, email)
  - Statut de la demande

#### 2. Collection `addresses`

- Gestion des adresses de résidence
- Structure :
  - `citizenId`: Référence au citoyen
  - Localisation (région, préfecture, commune, quartier)
  - Adresse précise
  - Date d'installation

#### 3. Collection `documents`

- Suivi des pièces justificatives
- Organisation :
  - `citizenId`: Référence au citoyen
  - Type de document
  - Référence au fichier stocké
  - Statut de validation

### 🗄️ Stockage des Documents

Un bucket dédié `citizen_documents` avec :

- Limite de taille : 10MB par fichier
- Types acceptés : Images et PDF
- Sécurité renforcée
- Gestion des permissions

### 🔒 Sécurité et Validation

1. **Validation des Données**

   - Schéma Zod complet pour chaque étape
   - Vérification des formats (email, téléphone)
   - Validation des mots de passe

2. **Sécurité des Documents**

   - Vérification des types de fichiers
   - Scan antivirus (à venir)
   - Stockage sécurisé

3. **Protection des Données**
   - Chiffrement des données sensibles
   - Gestion des permissions par rôle
   - Journalisation des accès

### 📝 Processus d'Inscription

1. **Création du Compte**

   - Validation des informations de base
   - Création du compte utilisateur
   - Attribution des permissions initiales

2. **Enregistrement du Profil**

   - Stockage des informations personnelles
   - Création de l'entrée dans `citizens`
   - Attribution d'un ID unique

3. **Gestion de l'Adresse**

   - Validation de l'adresse
   - Enregistrement dans `addresses`
   - Liaison avec le profil citoyen

4. **Traitement des Documents**
   - Upload sécurisé
   - Création des métadonnées
   - Statut initial "en attente"

### 🔄 Workflow de Validation

1. **Soumission**

   - Vérification complète des données
   - Création des enregistrements
   - Notification de réception

2. **Vérification**

   - Examen des documents
   - Validation des informations
   - Mise à jour des statuts

3. **Finalisation**
   - Approbation ou rejet
   - Notification au citoyen
   - Génération du certificat (si approuvé)

### 🛠️ Configuration Requise

```bash
# Variables d'environnement nécessaires
APPWRITE_ENDPOINT=votre_endpoint
APPWRITE_PROJECT_ID=votre_project_id
APPWRITE_API_KEY=votre_api_key
```

### 📊 Indexes et Performance

- Index sur email pour recherche rapide
- Index sur numéro de téléphone
- Index sur les références citoyens
- Optimisation des requêtes

### 🔍 Monitoring et Maintenance

- Suivi des inscriptions
- Surveillance des uploads
- Gestion des erreurs
- Sauvegarde automatique

TODO:

- liste demandes récentes
- certificats (liste tous les certificats)
- page signer certificats
- ajouter un agent
- gerer des agents
  Pour nettoyer complètement votre projet et supprimer le cache pnpm, suivez ces étapes :

### Misc.

1. Supprimez le dossier node_modules :

   ```
   rm -rf node_modules
   ```

2. Supprimez le fichier de verrouillage pnpm :

   ```
   rm pnpm-lock.yaml
   ```

3. Nettoyez le cache global de pnpm :

   ```
   pnpm store prune
   ```

4. Supprimez le cache local de pnpm (si existant) :

   ```
   rm -rf .pnpm-store
   ```

5. Nettoyez le cache de Next.js :

   ```
   rm -rf .next
   ```

6. Réinstallez les dépendances :

   ```
   pnpm install
   ```

7. Reconstruisez votre projet :

   ```
   pnpm run build
   ```

   Ces étapes devraient nettoyer complètement votre environnement de développement. Après cela, redémarrez votre application pour voir si le problème est résolu.

8. Orange money simulator test:
   ```sh
   https://mpayment.orange-money.com/mpayment-otp/main/7701901256
   Pour la connexion Simulateur
   MSISDN : 7701901256
   password : MerchantWP01256
   PIN: 9128
   ```
