import { Databases, IndexType } from "node-appwrite";
import { createAdminClient } from "./appwrite";
import { COLLECTIONS } from "./constant";

export const DATABASE_ID = "ncr_database";

// Collections pour les différents types d'utilisateurs
export const CITIZENS_COLLECTION_ID = COLLECTIONS.CITIZENS;
export const CHEFS_COLLECTION_ID = COLLECTIONS.CHEFS;
export const AGENTS_COLLECTION_ID = COLLECTIONS.AGENTS;
export const ADMINS_COLLECTION_ID = COLLECTIONS.ADMINS;

// Collections pour les données associées
export const DOCUMENTS_COLLECTION_ID = COLLECTIONS.DOCUMENTS;
export const CERTIFICATES_COLLECTION_ID = COLLECTIONS.CERTIFICATES;

export const QUARTIERS_COLLECTION_ID = COLLECTIONS.QUARTIERS;

export const BIRTH_DECLARATIONS_COLLECTION_ID = COLLECTIONS.BIRTH_DECLARATIONS;

export const PAYMENTS_COLLECTION_ID = COLLECTIONS.PAYMENTS;

export const PAYMENTS_METADATA_COLLECTION_ID = COLLECTIONS.PAYMENTS_METADATA;

// Collection pour les adresses (utilisée pour la vérification)
export const ADDRESSES_COLLECTION_ID = COLLECTIONS.ADDRESSES_COLLECTION;

// Collections pour la vérification des certificats
export const CERTIFICATE_VERIFICATIONS_COLLECTION_ID =
  COLLECTIONS.CERTIFICATE_VERIFICATIONS;
export const CERTIFICATE_METADATA_COLLECTION_ID =
  COLLECTIONS.CERTIFICATE_METADATA;

// Collections pour la gestion avancée
export const REVENUE_DISTRIBUTIONS_COLLECTION_ID =
  COLLECTIONS.REVENUE_DISTRIBUTIONS;
export const CHEF_SIGNATURES_COLLECTION_ID = COLLECTIONS.CHEF_SIGNATURES;

// Structure des collections
const collections = {
  [ADMINS_COLLECTION_ID]: {
    name: "Admins",
    attributes: [
      { key: "userId", type: "string", required: true },
      { key: "nom", type: "string", required: true },
      { key: "email", type: "string", required: true },
      { key: "telephone", type: "string", required: true },
      { key: "status", type: "string", required: true },
      { key: "role", type: "string", required: false, defaultValue: "admin" },
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "user_id",
        type: IndexType.Unique,
        attributes: ["userId"],
      },
      {
        key: "email",
        type: IndexType.Unique,
        attributes: ["email"],
      },
      {
        key: "name_fulltext",
        type: IndexType.Fulltext,
        attributes: ["nom", "prenom"],
      },
      {
        key: "status_role",
        type: IndexType.Key,
        attributes: ["status", "role"],
      },
    ],
  },

  [CHEFS_COLLECTION_ID]: {
    name: "Chefs",
    attributes: [
      { key: "userId", type: "string", required: true },
      { key: "nom", type: "string", required: true },
      { key: "email", type: "string", required: true },
      { key: "telephone", type: "string", required: true },
      { key: "status", type: "string", required: true },
      { key: "role", type: "string", required: false, defaultValue: "chef" },
      { key: "quartierId", type: "string", required: true },
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "user_id",
        type: IndexType.Unique,
        attributes: ["userId"],
      },
      {
        key: "quartier",
        type: IndexType.Unique,
        attributes: ["quartierId"],
      },
      {
        key: "email",
        type: IndexType.Unique,
        attributes: ["email"],
      },
      {
        key: "name_fulltext",
        type: IndexType.Fulltext,
        attributes: ["nom", "prenom"],
      },
      {
        key: "status_role",
        type: IndexType.Key,
        attributes: ["status", "role"],
      },
    ],
  },

  [AGENTS_COLLECTION_ID]: {
    name: "Agents",
    attributes: [
      { key: "userId", type: "string", required: true },
      { key: "nom", type: "string", required: true },
      { key: "email", type: "string", required: true },
      { key: "telephone", type: "string", required: true },
      { key: "status", type: "string", required: true },
      { key: "role", type: "string", required: false, defaultValue: "agent" },
      { key: "chefId", type: "string", required: true }, // Lien vers le chef de quartier
      { key: "quartierId", type: "string", required: true }, // Hérité du chef
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "user_id",
        type: IndexType.Unique,
        attributes: ["userId"],
      },
      {
        key: "chef",
        type: IndexType.Unique,
        attributes: ["chefId"],
      },
      {
        key: "quartier",
        type: IndexType.Unique,
        attributes: ["quartierId"],
      },
      {
        key: "name_fulltext",
        type: IndexType.Fulltext,
        attributes: ["nom", "prenom"],
      },
      {
        key: "status_role",
        type: IndexType.Key,
        attributes: ["status", "role"],
      },
    ],
  },

  [QUARTIERS_COLLECTION_ID]: {
    name: "Quartiers",
    attributes: [
      { key: "nom", type: "string", required: true },
      { key: "sousPrefecture", type: "string", required: true },
      { key: "prefecture", type: "string", required: true },
      { key: "commune", type: "string", required: true },
      { key: "region", type: "string", required: true },
      { key: "chefId", type: "string", required: false }, // Lien vers le chef actuel
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "nom_prefecture",
        type: IndexType.Unique,
        attributes: ["nom", "prefecture"],
      },
      {
        key: "nom_commune",
        type: IndexType.Unique,
        attributes: ["nom", "commune"],
      },
      {
        key: "chef",
        type: IndexType.Key,
        attributes: ["chefId"],
      },
      {
        key: "name_fulltext",
        type: IndexType.Fulltext,
        attributes: ["nom"],
      },
      {
        key: "location",
        type: IndexType.Key,
        attributes: ["prefecture", "commune"],
      },
    ],
  },

  // Mise à jour de la collection Citizens pour aligner avec le certificat
  [CITIZENS_COLLECTION_ID]: {
    name: "Citizens",
    attributes: [
      { key: "userId", type: "string", required: true },
      { key: "nom", type: "string", required: true },
      { key: "prenom", type: "string", required: true },
      { key: "dateNaissance", type: "string", required: true },
      { key: "lieuNaissance", type: "string", required: true },
      { key: "nomPere", type: "string", required: true },
      { key: "nomMere", type: "string", required: true },
      { key: "nationalite", type: "string", required: true },
      { key: "profession", type: "string", required: true },
      { key: "telephone", type: "string", required: true },
      { key: "email", type: "string", required: true },
      { key: "status", type: "string", required: true },
      { key: "role", type: "string", required: false, defaultValue: "citizen" },
      { key: "carteElecteur", type: "string", required: false },
      { key: "quartierId", type: "string", required: true },
      { key: "adressePrecise", type: "string", required: true },
      { key: "dateInstallation", type: "string", required: true },
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
      // Champs pour la gestion des transferts de quartier
      { key: "transferRequest", type: "string", required: false }, // JSON string
      { key: "transferStatus", type: "string", required: false }, // pending, partially_approved, completed, rejected
      { key: "transferApprovals", type: "string", required: false }, // JSON string
      { key: "transferCompletedAt", type: "string", required: false },
      { key: "transferRejection", type: "string", required: false }, // JSON string
    ],
    indexes: [
      {
        key: "user_id",
        type: IndexType.Unique,
        attributes: ["userId"],
      },
      {
        key: "quartier",
        type: IndexType.Unique,
        attributes: ["quartierId"],
      },
      {
        key: "email",
        type: IndexType.Unique,
        attributes: ["email"],
      },
      {
        key: "name_fulltext",
        type: IndexType.Fulltext,
        attributes: ["nom", "prenom"],
      },
      {
        key: "telephone_unique",
        type: IndexType.Unique,
        attributes: ["telephone"],
      },
      {
        key: "status_role",
        type: IndexType.Key,
        attributes: ["status", "role"],
      },
      {
        key: "quartier",
        type: IndexType.Key,
        attributes: ["quartierId"],
      },
    ],
  },

  // Pour les pièces justificatives des citoyens
  [DOCUMENTS_COLLECTION_ID]: {
    name: "Documents",
    attributes: {
      citizenId: { type: "string", required: true },
      type: { type: "string", required: true },
      fileId: { type: "string", required: true },
      status: { type: "string", required: true },
    },
    indexes: [
      {
        key: "citizen_docs_index",
        type: IndexType.Key,
        attributes: ["citizenId"],
      },
    ],
  },

  [CERTIFICATES_COLLECTION_ID]: {
    name: "Certificates",
    attributes: [
      { key: "reference", type: "string", required: true }, // Format: SFG-Z2D (exemple)
      { key: "citizenId", type: "string", required: true }, // ID du citoyen demandeur
      { key: "quartierId", type: "string", required: false }, // ID du quartier
      { key: "chefId", type: "string", required: true }, // ID du chef qui doit signer
      { key: "agentId", type: "string", required: false }, // ID de l'agent qui traite
      { key: "status", type: "string", required: true }, // pending, verified, ready, signed, delivered, rejected
      { key: "motif", type: "string", required: true }, // Motif de la demande
      { key: "type", type: "string", required: true }, // Type de certificat
      { key: "validite", type: "string", required: false, defaultValue: "3" }, // Durée de validité en mois
      // Suivi du traitement
      { key: "verifiedAt", type: "string", required: false }, // Date de vérification par l'agent
      { key: "verifiedBy", type: "string", required: false }, // ID de l'agent qui a vérifié
      { key: "verificationNotes", type: "string", required: false }, // Notes de vérification
      { key: "readyAt", type: "string", required: false }, // Date de préparation
      { key: "readyBy", type: "string", required: false }, // ID de qui a préparé
      { key: "signedAt", type: "string", required: false }, // Date de signature
      { key: "deliveredAt", type: "string", required: false }, // Date de délivrance
      { key: "deliveredBy", type: "string", required: false }, // ID de qui a délivré
      { key: "rejectedAt", type: "string", required: false }, // Date de rejet
      { key: "rejectedBy", type: "string", required: false }, // ID de la personne qui a rejeté
      { key: "rejectionReason", type: "string", required: false }, // Raison du rejet
      { key: "assignedAt", type: "string", required: false }, // Date d'assignation
      // Paiement
      { key: "price", type: "string", required: false }, // Prix en centimes
      { key: "isPaid", type: "string", required: false, defaultValue: "false" }, // Boolean as string
      { key: "paymentStatus", type: "string", required: false }, // Status du paiement
      { key: "paymentId", type: "string", required: false }, // ID de la transaction de paiement
      { key: "paidAt", type: "string", required: false }, // Date de paiement
      // Documents
      { key: "documents", type: "string", required: false }, // Array as JSON string
      { key: "documentUrl", type: "string", required: false }, // URL du document final
      { key: "documentId", type: "string", required: false }, // ID du fichier document
      { key: "downloadedAt", type: "string", required: false }, // Date de téléchargement
      // Métadonnées
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
      { key: "expiresAt", type: "string", required: false }, // Date d'expiration calculée
      // Historique des mises à jour
      { key: "updateHistory", type: "string", required: false }, // JSON string
    ],
    indexes: [
      {
        key: "reference_unique",
        type: IndexType.Unique,
        attributes: ["reference"],
      },
      {
        key: "citizen_certificates",
        type: IndexType.Key,
        attributes: ["citizenId"],
      },
      {
        key: "quartier_certificates",
        type: IndexType.Key,
        attributes: ["quartierId"],
      },
      {
        key: "chef_certificates",
        type: IndexType.Key,
        attributes: ["chefId"],
      },
      {
        key: "agent_certificates",
        type: IndexType.Key,
        attributes: ["agentId"],
      },
      {
        key: "status_index",
        type: IndexType.Key,
        attributes: ["status"],
      },
      {
        key: "payment_status_index",
        type: IndexType.Key,
        attributes: ["paymentStatus"],
      },
      {
        key: "payment_id_index",
        type: IndexType.Key,
        attributes: ["paymentId"],
      },
      {
        key: "is_paid_index",
        type: IndexType.Key,
        attributes: ["isPaid"],
      },
      {
        key: "type_index",
        type: IndexType.Key,
        attributes: ["type"],
      },
      {
        key: "expires_at_index",
        type: IndexType.Key,
        attributes: ["expiresAt"],
      },
      {
        key: "created_at_index",
        type: IndexType.Key,
        attributes: ["createdAt"],
      },
    ],
  },

  // Collection pour les vérifications de certificats
  [CERTIFICATE_VERIFICATIONS_COLLECTION_ID]: {
    name: "Certificate Verifications",
    attributes: [
      { key: "hash", type: "string", required: true },
      { key: "certificateId", type: "string", required: true },
      { key: "citizenId", type: "string", required: true },
      { key: "issuedAt", type: "string", required: true },
      { key: "expiresAt", type: "string", required: true },
      { key: "isValid", type: "boolean", required: true },
      { key: "isRevoked", type: "boolean", required: true },
      { key: "verificationCount", type: "integer", required: true },
      { key: "lastVerifiedAt", type: "string", required: false },
      { key: "metadataId", type: "string", required: true },
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "hash_unique",
        type: IndexType.Unique,
        attributes: ["hash"],
      },
      {
        key: "certificate_id",
        type: IndexType.Key,
        attributes: ["certificateId"],
      },
      {
        key: "citizen_id",
        type: IndexType.Key,
        attributes: ["citizenId"],
      },
      {
        key: "metadata_id",
        type: IndexType.Key,
        attributes: ["metadataId"],
      },
      {
        key: "expires_at",
        type: IndexType.Key,
        attributes: ["expiresAt"],
      },
      {
        key: "status",
        type: IndexType.Key,
        attributes: ["isValid", "isRevoked"],
      },
    ],
  },

  // Collection pour les adresses
  [ADDRESSES_COLLECTION_ID]: {
    name: "Addresses",
    attributes: [
      { key: "citizenId", type: "string", required: true },
      { key: "quartier", type: "string", required: true },
      { key: "commune", type: "string", required: true },
      { key: "region", type: "string", required: true },
      { key: "adressePrecise", type: "string", required: true },
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "citizen_id",
        type: IndexType.Key,
        attributes: ["citizenId"],
      },
      {
        key: "location",
        type: IndexType.Key,
        attributes: ["region", "commune", "quartier"],
      },
    ],
  },

  // Collection pour les métadonnées de certificats
  [CERTIFICATE_METADATA_COLLECTION_ID]: {
    name: "Certificate Metadata",
    attributes: [
      { key: "verificationId", type: "string", required: true },
      { key: "issuerType", type: "string", required: true },
      { key: "issuerId", type: "string", required: true },
      { key: "issuerName", type: "string", required: true },
      { key: "region", type: "string", required: true },
      { key: "commune", type: "string", required: true },
      { key: "quartier", type: "string", required: true },
      { key: "revocationReason", type: "string", required: false },
      { key: "revokedAt", type: "string", required: false },
      { key: "revokedBy", type: "string", required: false },
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "verification_id",
        type: IndexType.Unique,
        attributes: ["verificationId"],
      },
      {
        key: "issuer",
        type: IndexType.Key,
        attributes: ["issuerType", "issuerId"],
      },
      {
        key: "location",
        type: IndexType.Key,
        attributes: ["region", "commune", "quartier"],
      },
      {
        key: "issuer_name_fulltext",
        type: IndexType.Fulltext,
        attributes: ["issuerName"],
      },
    ],
  },

  // Collection pour les paiements
  [PAYMENTS_COLLECTION_ID]: {
    name: "Payments",
    attributes: [
      { key: "orderId", type: "string", required: true },
      { key: "userId", type: "string", required: true },
      { key: "amount", type: "string", required: true }, // Stored as string to avoid precision issues
      { key: "provider", type: "string", required: true },
      { key: "status", type: "string", required: true },
      { key: "paymentUrl", type: "string", required: false },
      { key: "payToken", type: "string", required: false },
      { key: "transactionId", type: "string", required: false },
      { key: "completedAt", type: "string", required: false },
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "order_id_unique",
        type: IndexType.Unique,
        attributes: ["orderId"],
      },
      {
        key: "user_payments",
        type: IndexType.Key,
        attributes: ["userId"],
      },
      {
        key: "status_index",
        type: IndexType.Key,
        attributes: ["status"],
      },
      {
        key: "provider_index",
        type: IndexType.Key,
        attributes: ["provider"],
      },
      {
        key: "created_at_index",
        type: IndexType.Key,
        attributes: ["createdAt"],
      },
    ],
  },

  // Collection pour les métadonnées de paiements
  [PAYMENTS_METADATA_COLLECTION_ID]: {
    name: "Payments Metadata",
    attributes: [
      { key: "paymentReference", type: "string", required: true },
      { key: "orderId", type: "string", required: true },
      { key: "certificateId", type: "string", required: true },
      { key: "type", type: "string", required: true },
      { key: "returnUrl", type: "string", required: true },
      { key: "createdAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "order_id_index",
        type: IndexType.Key,
        attributes: ["orderId"],
      },
      {
        key: "certificate_id_index",
        type: IndexType.Key,
        attributes: ["certificateId"],
      },
      {
        key: "payment_reference_index",
        type: IndexType.Key,
        attributes: ["paymentReference"],
      },
    ],
  },

  // Collection pour la distribution des revenus
  [REVENUE_DISTRIBUTIONS_COLLECTION_ID]: {
    name: "Revenue Distributions",
    attributes: [
      { key: "paymentId", type: "string", required: true },
      { key: "orderId", type: "string", required: true },
      { key: "totalAmount", type: "string", required: true },
      { key: "platformAmount", type: "string", required: true }, // 15%
      { key: "communeAmount", type: "string", required: true }, // 10%
      { key: "quartierAmount", type: "string", required: true }, // 25%
      { key: "partnerAmount", type: "string", required: true }, // 10%
      { key: "payrollAmount", type: "string", required: true }, // 40%
      { key: "distributedAt", type: "string", required: true },
      { key: "status", type: "string", required: true }, // pending, completed, failed
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
    ],
    indexes: [
      {
        key: "payment_id_unique",
        type: IndexType.Unique,
        attributes: ["paymentId"],
      },
      {
        key: "order_id_index",
        type: IndexType.Key,
        attributes: ["orderId"],
      },
      {
        key: "status_index",
        type: IndexType.Key,
        attributes: ["status"],
      },
      {
        key: "distributed_at_index",
        type: IndexType.Key,
        attributes: ["distributedAt"],
      },
    ],
  },

  // Collection pour les signatures des chefs (réutilisation optimisée)
  [CHEF_SIGNATURES_COLLECTION_ID]: {
    name: "Chef Signatures",
    attributes: [
      { key: "chefId", type: "string", required: true }, // ID unique du chef
      { key: "signatureFileId", type: "string", required: true }, // ID du fichier de signature dans le bucket
      { key: "signatureType", type: "string", required: true }, // Type: "digital", "handwritten"
      { key: "signatureHash", type: "string", required: true }, // Hash SHA-256 de la signature pour vérification d'intégrité
      { key: "isActive", type: "string", required: true, defaultValue: "true" }, // Boolean as string
      { key: "createdAt", type: "string", required: true },
      { key: "updatedAt", type: "string", required: true },
      { key: "lastUsedAt", type: "string", required: false }, // Dernière utilisation
      { key: "usageCount", type: "string", required: true, defaultValue: "0" }, // Nombre d'utilisations
      { key: "version", type: "string", required: true, defaultValue: "1" }, // Version de la signature
    ],
    indexes: [
      {
        key: "chef_id_unique",
        type: IndexType.Unique,
        attributes: ["chefId"],
      },
      {
        key: "is_active_index",
        type: IndexType.Key,
        attributes: ["isActive"],
      },
      {
        key: "created_at_index",
        type: IndexType.Key,
        attributes: ["createdAt"],
      },
      {
        key: "signature_hash_index",
        type: IndexType.Key,
        attributes: ["signatureHash"],
      },
    ],
  },
};

interface Attribute {
  key: string;
  type: "string";
  required: boolean;
  defaultValue?: string;
}

// Fonction utilitaire pour convertir les attributs
function normalizeAttribute(attr: any): Attribute {
  return {
    key: attr.key,
    type: "string", // Force le type littéral 'string'
    required: attr.required,
    defaultValue: attr.defaultValue,
  };
}

async function createAttribute(
  databases: Databases,
  databaseId: string,
  collectionId: string,
  attribute: Attribute
) {
  try {
    await databases.createStringAttribute(
      databaseId,
      collectionId,
      attribute.key,
      255,
      attribute.required,
      attribute.defaultValue
    );
    console.log(`✓ Attribute ${attribute.key} created successfully`);
  } catch (error: any) {
    if (error.code !== 409) {
      console.error(`× Error creating attribute ${attribute.key}:`, error);
      throw error;
    }
    console.log(`⚠ Attribute ${attribute.key} already exists`);
  }
}

async function createIndex(
  databases: Databases,
  databaseId: string,
  collectionId: string,
  index: { key: string; type: IndexType; attributes: string[] }
) {
  try {
    await databases.createIndex(
      databaseId,
      collectionId,
      index.key,
      index.type,
      index.attributes
    );
    console.log(`✓ Index ${index.key} created successfully`);
  } catch (error: any) {
    if (error.code !== 409) {
      console.error(`× Error creating index ${index.key}:`, error);
      throw error;
    }
    console.log(`⚠ Index ${index.key} already exists`);
  }
}

export async function initializeDatabase(force: boolean = false) {
  console.log(" Starting database initialization...");
  const { databases } = await createAdminClient();

  try {
    // Create database
    try {
      await databases.create(DATABASE_ID, "NCR Database");
      console.log("✓ Database created successfully");
    } catch (error: any) {
      if (error.code !== 409 || force) throw error;
      console.log("⚠ Database already exists");
    }

    // Initialize collections
    for (const [collectionId, config] of Object.entries(collections)) {
      console.log(`\n📁 Initializing collection: ${config.name}`);

      // Create collection
      try {
        await databases.createCollection(
          DATABASE_ID,
          collectionId,
          config.name
        );
        console.log(`✓ Collection ${config.name} created successfully`);
      } catch (error: any) {
        if (error.code !== 409 || force) throw error;
        console.log(`⚠ Collection ${config.name} already exists`);
      }

      // Create attributes
      console.log("\nCreating attributes...");
      const attributes = Array.isArray(config.attributes)
        ? config.attributes.map(normalizeAttribute)
        : Object.entries(config.attributes).map(([key, value]) =>
            normalizeAttribute({ key, ...value })
          );

      for (const attribute of attributes) {
        await createAttribute(databases, DATABASE_ID, collectionId, attribute);
      }

      // Create indexes
      if (config.indexes) {
        console.log("\nCreating indexes...");
        for (const index of config.indexes) {
          await createIndex(databases, DATABASE_ID, collectionId, index);
        }
      }
    }

    console.log("\n✅ Database initialization completed successfully");
  } catch (error) {
    console.error("\n❌ Error during database initialization:", error);
    throw error;
  }
}
