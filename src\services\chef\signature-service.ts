import { createAdminClient } from "@/lib/server/appwrite";
import { BUCKETS } from "@/lib/server/constant";
import {
  CHEF_SIGNATURES_COLLECTION_ID,
  DATABASE_ID,
} from "@/lib/server/database";
import { createHash } from "crypto";
import { ID, Query } from "node-appwrite";

export interface ChefSignature {
  $id: string;
  chefId: string;
  signatureFileId: string;
  signatureType: "digital" | "handwritten";
  signatureHash: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastUsedAt?: string;
  usageCount: number;
  version: number;
}

export interface CreateSignatureParams {
  chefId: string;
  signatureData: string;
  signatureType: "digital" | "handwritten";
}

/**
 * Service robuste et sécurisé pour la gestion des signatures des chefs
 * Implémente la réutilisation optimisée avec vérification d'intégrité
 */
export class ChefSignatureService {
  private static instance: ChefSignatureService;

  public static getInstance(): ChefSignatureService {
    if (!ChefSignatureService.instance) {
      ChefSignatureService.instance = new ChefSignatureService();
    }
    return ChefSignatureService.instance;
  }

  /**
   * Génère un hash SHA-256 sécurisé pour la signature
   */
  private generateSignatureHash(signatureData: string): string {
    return createHash("sha256").update(signatureData).digest("hex");
  }

  /**
   * Récupère la signature active d'un chef
   */
  async getActiveSignature(chefId: string): Promise<ChefSignature | null> {
    try {
      const { databases } = await createAdminClient();

      const signatures = await databases.listDocuments(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        [
          Query.equal("chefId", chefId),
          Query.equal("isActive", "true"),
          Query.limit(1),
        ]
      );

      if (signatures.documents.length === 0) {
        return null;
      }

      const doc = signatures.documents[0];
      return {
        $id: doc.$id,
        chefId: doc.chefId,
        signatureFileId: doc.signatureFileId,
        signatureType: doc.signatureType as "digital" | "handwritten",
        signatureHash: doc.signatureHash,
        isActive: doc.isActive === "true",
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
        lastUsedAt: doc.lastUsedAt,
        usageCount: parseInt(doc.usageCount || "0"),
        version: parseInt(doc.version || "1"),
      };
    } catch (error) {
      console.error("Erreur lors de la récupération de la signature:", error);
      throw new Error("Impossible de récupérer la signature du chef");
    }
  }

  /**
   * Crée ou met à jour la signature d'un chef (upsert sécurisé)
   */
  async upsertSignature(params: CreateSignatureParams): Promise<ChefSignature> {
    try {
      const { databases, storage } = await createAdminClient();
      const now = new Date().toISOString();

      // Générer le hash de la signature pour vérification d'intégrité
      const signatureHash = this.generateSignatureHash(params.signatureData);

      // Convertir le base64 en Buffer pour le stockage
      const base64Data = params.signatureData.replace(
        /^data:image\/\w+;base64,/,
        ""
      );
      const buffer = Buffer.from(base64Data, "base64");

      // Vérifier si une signature existe déjà
      const existingSignature = await this.getActiveSignature(params.chefId);

      if (existingSignature) {
        // Vérifier si la signature a changé
        if (existingSignature.signatureHash === signatureHash) {
          // Signature identique, pas besoin de mise à jour
          return existingSignature;
        }

        // Supprimer l'ancien fichier de signature
        try {
          await storage.deleteFile(
            BUCKETS.SIGNATURES,
            existingSignature.signatureFileId
          );
        } catch (error) {
          console.warn(
            "Impossible de supprimer l'ancien fichier de signature:",
            error
          );
        }

        // Créer un nouveau fichier de signature
        const signatureFile = await storage.createFile(
          BUCKETS.SIGNATURES,
          ID.unique(),
          new File(
            [new Uint8Array(buffer)],
            `chef-signature-${params.chefId}-v${
              existingSignature.version + 1
            }.png`,
            {
              type: "image/png",
            }
          )
        );

        // Mettre à jour la signature existante
        const updatedDoc = await databases.updateDocument(
          DATABASE_ID,
          CHEF_SIGNATURES_COLLECTION_ID,
          existingSignature.$id,
          {
            signatureFileId: signatureFile.$id,
            signatureType: params.signatureType,
            signatureHash,
            updatedAt: now,
            version: (existingSignature.version + 1).toString(),
            isActive: "true",
          }
        );

        return {
          $id: updatedDoc.$id,
          chefId: updatedDoc.chefId,
          signatureFileId: updatedDoc.signatureFileId,
          signatureType: updatedDoc.signatureType as "digital" | "handwritten",
          signatureHash: updatedDoc.signatureHash,
          isActive: updatedDoc.isActive === "true",
          createdAt: updatedDoc.createdAt,
          updatedAt: updatedDoc.updatedAt,
          lastUsedAt: updatedDoc.lastUsedAt,
          usageCount: parseInt(updatedDoc.usageCount || "0"),
          version: parseInt(updatedDoc.version || "1"),
        };
      } else {
        // Créer une nouvelle signature
        const signatureFile = await storage.createFile(
          BUCKETS.SIGNATURES,
          ID.unique(),
          new File(
            [new Uint8Array(buffer)],
            `chef-signature-${params.chefId}-v1.png`,
            {
              type: "image/png",
            }
          )
        );

        const newDoc = await databases.createDocument(
          DATABASE_ID,
          CHEF_SIGNATURES_COLLECTION_ID,
          ID.unique(),
          {
            chefId: params.chefId,
            signatureFileId: signatureFile.$id,
            signatureType: params.signatureType,
            signatureHash,
            isActive: "true",
            createdAt: now,
            updatedAt: now,
            usageCount: "0",
            version: "1",
          }
        );

        return {
          $id: newDoc.$id,
          chefId: newDoc.chefId,
          signatureFileId: newDoc.signatureFileId,
          signatureType: newDoc.signatureType as "digital" | "handwritten",
          signatureHash: newDoc.signatureHash,
          isActive: newDoc.isActive === "true",
          createdAt: newDoc.createdAt,
          updatedAt: newDoc.updatedAt,
          lastUsedAt: newDoc.lastUsedAt,
          usageCount: parseInt(newDoc.usageCount || "0"),
          version: parseInt(newDoc.version || "1"),
        };
      }
    } catch (error) {
      console.error(
        "Erreur lors de la création/mise à jour de la signature:",
        error
      );
      throw new Error("Impossible de sauvegarder la signature du chef");
    }
  }

  /**
   * Utilise la signature d'un chef et met à jour les statistiques d'usage
   */
  async useSignature(chefId: string): Promise<ChefSignature | null> {
    try {
      const signature = await this.getActiveSignature(chefId);

      if (!signature) {
        return null;
      }

      const { databases } = await createAdminClient();
      const now = new Date().toISOString();
      const newUsageCount = signature.usageCount + 1;

      const updatedDoc = await databases.updateDocument(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        signature.$id,
        {
          lastUsedAt: now,
          usageCount: newUsageCount.toString(),
          updatedAt: now,
        }
      );

      return {
        $id: updatedDoc.$id,
        chefId: updatedDoc.chefId,
        signatureFileId: updatedDoc.signatureFileId,
        signatureType: updatedDoc.signatureType as "digital" | "handwritten",
        signatureHash: updatedDoc.signatureHash,
        isActive: updatedDoc.isActive === "true",
        createdAt: updatedDoc.createdAt,
        updatedAt: updatedDoc.updatedAt,
        lastUsedAt: updatedDoc.lastUsedAt,
        usageCount: newUsageCount,
        version: parseInt(updatedDoc.version || "1"),
      };
    } catch (error) {
      console.error("Erreur lors de l'utilisation de la signature:", error);
      throw new Error("Impossible d'utiliser la signature du chef");
    }
  }

  /**
   * Vérifie si un chef a une signature active
   */
  async hasActiveSignature(chefId: string): Promise<boolean> {
    try {
      const signature = await this.getActiveSignature(chefId);
      return signature !== null && signature.isActive;
    } catch (error) {
      console.error("Erreur lors de la vérification de la signature:", error);
      return false;
    }
  }

  /**
   * Récupère le fichier de signature pour copie
   */
  async getSignatureFile(signatureFileId: string): Promise<ArrayBuffer> {
    try {
      const { storage } = await createAdminClient();
      return await storage.getFileDownload(BUCKETS.SIGNATURES, signatureFileId);
    } catch (error) {
      console.error(
        "Erreur lors de la récupération du fichier de signature:",
        error
      );
      throw new Error("Impossible de récupérer le fichier de signature");
    }
  }
}

export const chefSignatureService = ChefSignatureService.getInstance();
