"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { motion } from "framer-motion";
import { FileSignature, Plus, Clock, CheckCircle } from "lucide-react";
import { useState, useEffect } from "react";
import { SignaturePadComponent } from "./signature-pad";
import { chefSignatureService } from "@/services/chef/signature-service";

interface ChefSignature {
  $id: string;
  chefId: string;
  signatureFileId: string;
  signatureType: "digital" | "handwritten";
  signatureHash: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastUsedAt?: string;
  usageCount: number;
  version: number;
}

interface SignatureSelectionProps {
  chefId: string;
  onSignatureSelected: (signatureData: string) => void;
  title?: string;
  description?: string;
  id?: string;
  disabled?: boolean;
}

export function SignatureSelection({
  chefId,
  onSignatureSelected,
  title = "Signature du chef de quartier",
  description = "Sélectionnez votre signature ou créez-en une nouvelle",
  id,
  disabled = false,
}: SignatureSelectionProps) {
  const { toast } = useToast();
  const [existingSignature, setExistingSignature] = useState<ChefSignature | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("existing");
  const [signaturePreview, setSignaturePreview] = useState<string | null>(null);

  useEffect(() => {
    const loadExistingSignature = async () => {
      try {
        setIsLoading(true);
        const signature = await chefSignatureService.getActiveSignature(chefId);
        setExistingSignature(signature);
        
        if (signature) {
          // Charger l'aperçu de la signature
          try {
            const signatureFile = await chefSignatureService.getSignatureFile(signature.signatureFileId);
            const blob = new Blob([signatureFile], { type: 'image/png' });
            const url = URL.createObjectURL(blob);
            setSignaturePreview(url);
          } catch (error) {
            console.warn("Impossible de charger l'aperçu de la signature:", error);
          }
        } else {
          // Pas de signature existante, aller directement à l'onglet nouveau
          setActiveTab("new");
        }
      } catch (error) {
        console.error("Erreur lors du chargement de la signature:", error);
        toast({
          title: "Erreur",
          description: "Impossible de charger votre signature existante",
          variant: "error",
        });
        setActiveTab("new");
      } finally {
        setIsLoading(false);
      }
    };

    loadExistingSignature();
  }, [chefId, toast]);

  const handleUseExistingSignature = async () => {
    if (!existingSignature || !signaturePreview) {
      toast({
        title: "Erreur",
        description: "Aucune signature disponible",
        variant: "error",
      });
      return;
    }

    try {
      // Convertir l'image en base64 pour l'utiliser
      const response = await fetch(signaturePreview);
      const blob = await response.blob();
      const reader = new FileReader();
      reader.onload = () => {
        const base64Data = reader.result as string;
        onSignatureSelected(base64Data);
      };
      reader.readAsDataURL(blob);
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible d'utiliser la signature existante",
        variant: "error",
      });
    }
  };

  const handleNewSignature = (signatureData: string) => {
    onSignatureSelected(signatureData);
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-2xl mx-auto" id={id}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <p className="text-sm text-muted-foreground">{description}</p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto" id={id}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileSignature className="w-5 h-5 text-accent-primary" />
          {title}
        </CardTitle>
        <p className="text-sm text-muted-foreground">{description}</p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={`grid w-full ${existingSignature ? 'grid-cols-2' : 'grid-cols-1'} mb-6`}>
            {existingSignature && (
              <TabsTrigger value="existing" className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                Signature existante
              </TabsTrigger>
            )}
            <TabsTrigger value="new" className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Nouvelle signature
            </TabsTrigger>
          </TabsList>

          {existingSignature && (
            <TabsContent value="existing" className="space-y-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                <div className="bg-neutral-50 rounded-lg p-4 border">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-neutral-900">Votre signature</h4>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        Version {existingSignature.version}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {existingSignature.usageCount} utilisations
                      </Badge>
                    </div>
                  </div>
                  
                  {signaturePreview && (
                    <div className="bg-white border rounded-md p-4 mb-4">
                      <img 
                        src={signaturePreview} 
                        alt="Aperçu de la signature" 
                        className="max-h-32 mx-auto"
                      />
                    </div>
                  )}
                  
                  <div className="flex items-center gap-2 text-sm text-neutral-600 mb-4">
                    <Clock className="w-4 h-4" />
                    <span>
                      Créée le {new Date(existingSignature.createdAt).toLocaleDateString()}
                      {existingSignature.lastUsedAt && (
                        <span className="ml-2">
                          • Dernière utilisation: {new Date(existingSignature.lastUsedAt).toLocaleDateString()}
                        </span>
                      )}
                    </span>
                  </div>
                  
                  <Button 
                    onClick={handleUseExistingSignature}
                    disabled={disabled}
                    className="w-full bg-gradient-to-r from-accent-primary to-accent-secondary hover:from-accent-primary/90 hover:to-accent-secondary/90 text-white"
                  >
                    <FileSignature className="w-4 h-4 mr-2" />
                    Utiliser cette signature
                  </Button>
                </div>
              </motion.div>
            </TabsContent>
          )}

          <TabsContent value="new" className="space-y-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <SignaturePadComponent
                onSave={handleNewSignature}
                title={existingSignature ? "Créer une nouvelle signature" : "Votre signature"}
                description={existingSignature ? "Cette signature remplacera votre signature actuelle" : "Veuillez signer dans le cadre ci-dessous"}
                saveButtonText="Valider la signature"
                disabled={disabled}
              />
            </motion.div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
