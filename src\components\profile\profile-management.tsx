"use client";

import { CERTIFICATE_STATUS, ROLES } from "@/actions/auth/constants";
import { CertificateUpdateForm } from "@/components/dashboard/citizen/certificate-update-form";
import { QuartierTransferForm } from "@/components/dashboard/citizen/quartier-transfer-form";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { UserAvatar } from "@/components/ui/user-avatar";
import { useCitizenCertificates } from "@/hooks/use-citizen-certificates";
import type { ExtendedUserDetails } from "@/hooks/use-user";
import { motion } from "framer-motion";
import { FileText, MapPin, Settings, User } from "lucide-react";
import { useState } from "react";

interface ProfileManagementProps {
  user: ExtendedUserDetails;
}

export function ProfileManagement({ user }: ProfileManagementProps) {
  const [activeTab, setActiveTab] = useState("profile");
  const [refreshKey, setRefreshKey] = useState(0);
  const { certificates, isLoading: isLoadingCertificates } =
    useCitizenCertificates();

  // Filtrer les certificats qui peuvent être mis à jour
  const updatableCertificates =
    certificates?.filter((cert) =>
      [CERTIFICATE_STATUS.REJECTED, CERTIFICATE_STATUS.SUBMITTED].includes(
        cert.status
      )
    ) || [];

  // Vérifier si l'utilisateur peut accéder au transfert de quartier
  const canTransferQuartier =
    user.prefs?.role === ROLES.CITIZEN || user.prefs?.role === ROLES.AGENT;

  const handleUpdate = () => {
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <div className="space-y-6">
      {/* En-tête du profil */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8"
      >
        {/* Cercles décoratifs */}
        <div className="absolute -top-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
        <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-secondary/5 rounded-full blur-2xl" />

        <div className="relative flex items-center gap-6">
          <div className="w-24 h-24 ring-4 ring-white shadow-xl rounded-full overflow-hidden">
            <UserAvatar
              name={user.name}
              src={user.prefs?.avatarUrl}
              size="lg"
              showStatus
              status="online"
              className="w-full h-full"
            />
          </div>
          <div className="flex-1">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent">
              {user.name}
            </h1>
            <p className="text-neutral-600 mt-1">{user.email}</p>
            <div className="flex items-center gap-4 mt-3">
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-accent-primary" />
                <span className="text-sm text-neutral-700">
                  {user.quartier?.name || "Quartier non défini"}
                </span>
              </div>
              <Badge variant="outline" className="text-xs">
                {user.prefs?.role || "Citoyen"}
              </Badge>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Onglets de gestion */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 overflow-hidden"
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList
            className={`grid w-full ${
              canTransferQuartier ? "grid-cols-4" : "grid-cols-3"
            } bg-neutral-50/50 p-1 m-4 rounded-xl`}
          >
            <TabsTrigger
              value="profile"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <User className="w-4 h-4" />
              <span className="hidden sm:inline">Profil</span>
            </TabsTrigger>
            {canTransferQuartier && (
              <TabsTrigger
                value="quartier"
                className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
              >
                <MapPin className="w-4 h-4" />
                <span className="hidden sm:inline">Quartier</span>
              </TabsTrigger>
            )}
            <TabsTrigger
              value="certificates"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <FileText className="w-4 h-4" />
              <span className="hidden sm:inline">Certificats</span>
              {updatableCertificates.length > 0 && (
                <Badge variant="error" className="ml-1 h-5 w-5 p-0 text-xs">
                  {updatableCertificates.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="settings"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <Settings className="w-4 h-4" />
              <span className="hidden sm:inline">Paramètres</span>
            </TabsTrigger>
          </TabsList>

          {/* Contenu des onglets */}
          <div className="p-6">
            <TabsContent value="profile" className="mt-0">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <Card className="border-0 shadow-none">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="w-5 h-5 text-accent-primary" />
                      Informations personnelles
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-neutral-700">
                          Nom complet
                        </label>
                        <p className="text-neutral-900 font-medium">
                          {user.name}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-neutral-700">
                          Email
                        </label>
                        <p className="text-neutral-900">{user.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-neutral-700">
                          Quartier
                        </label>
                        <p className="text-neutral-900">
                          {user.quartier?.name || "Non défini"}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-neutral-700">
                          Statut
                        </label>
                        <Badge variant="outline">
                          {user.prefs?.role || "Citoyen"}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {canTransferQuartier && (
              <TabsContent value="quartier" className="mt-0">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  key={`quartier-${refreshKey}`}
                >
                  <QuartierTransferForm
                    citizen={user}
                    onUpdate={handleUpdate}
                  />
                </motion.div>
              </TabsContent>
            )}

            <TabsContent value="certificates" className="mt-0">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
                key={`certificates-${refreshKey}`}
              >
                {isLoadingCertificates ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-primary"></div>
                  </div>
                ) : updatableCertificates.length > 0 ? (
                  <>
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-neutral-900 mb-2">
                        Certificats à mettre à jour
                      </h3>
                      <p className="text-sm text-neutral-600">
                        Vous avez {updatableCertificates.length} certificat(s)
                        qui nécessite(nt) votre attention.
                      </p>
                    </div>
                    {updatableCertificates.map((certificate) => (
                      <CertificateUpdateForm
                        key={certificate.$id}
                        certificate={certificate}
                        onUpdate={handleUpdate}
                      />
                    ))}
                  </>
                ) : (
                  <Card className="border-0 shadow-none">
                    <CardContent className="text-center py-12">
                      <FileText className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-neutral-900 mb-2">
                        Aucun certificat à mettre à jour
                      </h3>
                      <p className="text-neutral-600">
                        Tous vos certificats sont à jour ou en cours de
                        traitement.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </motion.div>
            </TabsContent>

            <TabsContent value="settings" className="mt-0">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <Card className="border-0 shadow-none">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="w-5 h-5 text-accent-primary" />
                      Paramètres du compte
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                        <div>
                          <h4 className="font-medium text-neutral-900">
                            Notifications
                          </h4>
                          <p className="text-sm text-neutral-600">
                            Recevoir des notifications par email
                          </p>
                        </div>
                        <Badge variant="outline">Activé</Badge>
                      </div>
                      <div className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                        <div>
                          <h4 className="font-medium text-neutral-900">
                            Sécurité
                          </h4>
                          <p className="text-sm text-neutral-600">
                            Authentification à deux facteurs
                          </p>
                        </div>
                        <Badge variant="outline">Désactivé</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </div>
        </Tabs>
      </motion.div>
    </div>
  );
}
